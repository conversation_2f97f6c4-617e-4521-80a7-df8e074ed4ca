import { API } from '@mtech-common/http'
export const NAME = 'fileService'

const APIS = {
  // 文件上传-公共类型
  uploadPublicFile: (data = []) => {
    return API.post(`/file/user/file/uploadPublic?useType=1`, data, {
      headers: { 'Content-Type': 'multipart/form-data' }
    })
  },
  //文件上传-私密类型
  uploadPrivateFile: (data = {}) => {
    return API.post(`/file/user/file/uploadPrivate?useType=2`, data)
  },
  //文件删除--公开文件
  deletePublicFile: (data = {}) => {
    return API.get(`/file/user/file/deletePublicFile`, data)
  },
  //文件删除--私密文件
  deletePrivateFile: (data = {}) => {
    return API.get(`/file/user/file/deletePrivateFile`, data)
  },
  // 文件预览
  filePreview: (data = {}) => API.get(`/file/user/file/mtPreview`, data),
  // 文件预览 (别名方法，与其他项目保持一致)
  getMtPreview: (data = {}) => API.get(`/file/user/file/mtPreview`, data),
  // 文件下载--公开文件
  downloadPublicFile: (data = {}) =>
  API.get(`/file/user/file/downloadPublicFile`, data, {
    responseType: 'blob'
  }),
  // 文件下载-私密文件
  downloadPrivateFile: (data = {}) =>
    API.get(`/file/user/file/downloadPrivateFile?useType=2`, data, {
      responseType: 'blob'
    })
}

export default APIS
