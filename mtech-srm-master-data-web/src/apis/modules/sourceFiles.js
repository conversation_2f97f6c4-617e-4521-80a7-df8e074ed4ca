import { API } from '@mtech-common/http'
export const NAME = 'sourceFiles'
const PROXY_MASTER = '/masterDataManagement/tenant'
const PROXY_SOURCING = '/sourcing/tenant'

// 采购组织
export const queryBusinessOrg = (data = {}) => {
  return API.get(
    `${PROXY_MASTER}/business-organization/getByOrgIdAndBgOrgTypeCode?BU_CODE=${localStorage.getItem(
      'currentBu'
    )}`,
    data
  )
}
// 品类查询---old
export const getCategoryPartnerRelations = (data = {}) => {
  return API.post(`/supplier/tenant/buyer/partner/relation/getCategoryPartnerRelations`, data)
}
// 品类查询
export const criteriaQuery = (data = {}) => {
  return API.post(`${PROXY_MASTER}/category/criteria-query`, data)
}
// 批量删除
export const batchDelete = (data = {}) => {
  return API.delete(`${PROXY_MASTER}/supply-source-list/batch-delete`, data)
}
// 批量更新状态
export const batchInvalid = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/batchInvalid`, data)
}
// 新增 -货源清单
export const batchAdd = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/batch-add`, data)
}
// 新增并同步 -货源清单
export const andSync = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/batch-add-and-sync`, data)
}
// 批量更新
export const batchUpdate = (data = {}) => {
  return API.put(`${PROXY_MASTER}/supply-source-list/batch-update`, data)
}
// 批量更新并同步
export const batchUpdateAndSync = (data = {}) => {
  return API.put(`${PROXY_MASTER}/supply-source-list/batch-update-and-sync`, data)
}
// 批量导入
export const importData = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/import-data`, data)
}
// 导出模板
export const exportData = (data = {}) => {
  return API.get(`${PROXY_MASTER}/supply-source-list/export-data`, data, {
    responseType: 'blob'
  })
}
// 查询详情
export const queryDetail = (data = {}) => {
  return API.get(`${PROXY_MASTER}/supply-source-list/queryDetail`, data)
}
// 通过工厂 品类 取交集物料
export const siteids = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/query-item-list-by-siteids`, data)
}
// new --- 公司
export const company = (data = {}) => {
  return API.get(`/masterDataManagement/tenant/supply-source-list/company`, data)
}
// new --- 供应商
export const querySupplier = (data = {}) => {
  return API.post(`/masterDataManagement/tenant/supply-source-list/querySupplier`, data)
}
// new --- 品类
export const queryCategorys = (data = {}) => {
  return API.post(`/masterDataManagement/tenant/supply-source-list/queryCategorys`, data)
}

///----------------->>>>>>>货源改造

//公司延用--> new --- 公司

// 供应商
export const queryIntersectionSupplier = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/queryIntersectionSupplier`, data)
}
//品类
export const queryIntersectionCategory = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/queryIntersectionCategory`, data)
}
//工厂
export const queryUnionSetSite = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/queryUnionSetSite`, data)
}
//物料
export const queryUnionSetItem = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/queryUnionSetItem`, data)
}
//采购组织
export const queryIntersectionBusiness = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/queryIntersectionBusiness`, data)
}
//新增
export const multiAdd = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/multiAdd`, data)
}
//新增--并同步
export const multiAddAndSync = (data = {}) => {
  return API.post(`${PROXY_MASTER}/supply-source-list/multiAddAndSync`, data)
}
// 样品确认品类配置批量删除
export const deleteSampleConfirm = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/sampleConfirmationConfig/deleteByIds`, data)
}
// 样品确认品类配置导入
export const importSampleConfirm = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/sampleConfirmationConfig/import`, data, {
    responseType: 'blob'
  })
}
// 样品确认品类配置下载导入模板
export const exportSampleConfirm = (data = {}) => {
  return API.get(`${PROXY_SOURCING}/sampleConfirmationConfig/exportTemplate`, data, {
    responseType: 'blob'
  })
}
// 样品确认品类配置添加修改
export const saveSampleConfirm = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/sampleConfirmationConfig/saveAndUpdate`, data)
}
// 样品确认品类配置更新状态(生效、失效)
export const updateStatusSampleConfirm = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/sampleConfirmationConfig/updateStatusByIds`, data)
}
// 豁免申请单批量删除
export const deleteExemptionApply = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/exemptionApplyInfo/deleteByIds`, data)
}
// 样品确认品类配置导入
export const importExemptionApply = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/exemptionApplyInfo/import`, data, {
    responseType: 'blob'
  })
}
// 样品确认品类配置下载导入模板
export const exportExemptionApply = (data = {}) => {
  return API.get(`${PROXY_SOURCING}/exemptionApplyInfo/exportTemplate`, data, {
    responseType: 'blob'
  })
}
// 豁免申请单添加修改
export const saveExemptionApply = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/exemptionApplyInfo/saveAndUpdate`, data)
}
// 豁免申请单提交
export const submitExemptionApply = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/exemptionApplyInfo/submit`, data)
}
// 同步至QMS
export const syncExemptionApply = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/exemptionApplyInfo/syncToQMS`, data)
}
// 样品确认单校验
export const checkExemptionApply = (data = {}) => {
  return API.post(`${PROXY_SOURCING}/sampleConfirmationConfig/checkResult`, data)
}
// 样品确认单过程查询-分页查询
export const pageSampleProcessApi = (data = {}) => {
  return API.post(`${PROXY_MASTER}/sampleConfirmationProcess/pagedQuery`, data)
}
// 样品确认单过程查询-导出
export const exportSampleProcessApi = (data = {}) => {
  return API.post(`${PROXY_MASTER}/sampleConfirmationProcess/export`, data, {
    responseType: 'blob'
  })
}
// 样品确认单过程查询-附件列表
export const fileListSampleProcessApi = (data = {}) => {
  return API.post(`${PROXY_MASTER}/sampleConfirmationProcess/fileList`, data)
}
// 样品确认单过程查询-流程状态列表
export const statusListSampleProcessApi = (data = {}) => {
  return API.post(`${PROXY_MASTER}/sampleConfirmationProcess/statusList`, data)
}
