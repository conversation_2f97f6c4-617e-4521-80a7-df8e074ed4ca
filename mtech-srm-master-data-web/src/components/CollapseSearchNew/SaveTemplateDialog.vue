<template>
  <mt-dialog
    ref="dialog"
    css-class="save-template-dialog"
    :header="dialogTitle"
    :buttons="buttons"
    @close="handleClose"
    :open="onOpen"
    :height="250"
    :width="440"
  >
    <div class="dialog-content">
      <mt-form ref="ruleForm" :model="formData" :rules="dialogRules" autocomplete="off">
        <mt-form-item prop="name" :label="$t('模板名')">
          <mt-input
            maxlength="16"
            v-model="formData.name"
            :show-clear-button="true"
            :disabled="false"
            :placeholder="$t('请输入模板名称')"
          ></mt-input>
        </mt-form-item>
      </mt-form>
    </div>
  </mt-dialog>
</template>

<script>
import MtDialog from '@mtech-ui/dialog'
import { API } from '@mtech-common/http'

const saveUserMemory = '/lowcodeWeb/tenant/user-memory/save'

export default {
  components: {
    MtDialog
  },
  props: {
    dialogData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogTitle: this.$t('保存搜索模板'),
      formData: { name: '' },
      dialogRules: {
        name: [
          { required: true, message: this.$t('请输入模板名称'), trigger: 'blur' },
          { min: 1, max: 16, message: this.$t('模板名称长度为1-16个字符'), trigger: 'blur' }
        ]
      },
      buttons: [
        {
          click: this.handleClose,
          buttonModel: { content: this.$t('取消') }
        },
        {
          click: this.saveSearchTemplate,
          buttonModel: { isPrimary: 'true', content: this.$t('确定') }
        }
      ]
    }
  },
  mounted() {
    this.$refs.dialog.ejsRef.show()
    this.$refs.ruleForm.resetFields()
  },
  methods: {
    onOpen(args) {
      args.preventFocus = true
    },
    handleClose() {
      this.$emit('handleAddDialogShow', false)
    },
    saveSearchTemplate() {
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 检查模板名是否重复
          if (this.dialogData.templateNames.includes(this.formData.name)) {
            this.$toast({ content: this.$t('模板名不能重复'), type: 'warning' })
            return
          }

          // 获取当前网格内存数据
          let _gridInfo = JSON.parse(sessionStorage.getItem(this.dialogData.gridId) || '{}')
          let _gridMemory = _gridInfo.gridMemory || _gridInfo

          // 创建新的搜索模板
          const newTemplate = {
            templateName: this.formData.name,
            searchRule: this.dialogData.searchRules,
            searchConditions: this.dialogData.searchConditions || {}, // 保存搜索条件值
            visibleConditions: this.dialogData.visibleConditions || [], // 保存显示的搜索条件
            conditionOrder: this.dialogData.conditionOrder || [] // 保存搜索条件顺序
          }

          // 添加到搜索模板列表
          if (_gridMemory?.searchTemplates?.length > 0) {
            _gridMemory.searchTemplates.push(newTemplate)
          } else {
            _gridMemory.searchTemplates = [newTemplate]
          }

          // 统一存储格式
          sessionStorage.setItem(
            this.dialogData.gridId,
            JSON.stringify({ gridMemory: _gridMemory })
          )

          // 调用API保存到服务器
          API.post(saveUserMemory, {
            gridId: this.dialogData.gridId,
            gridMemory: _gridMemory
          })
            .then((res) => {
              if (res.code === 200) {
                this.$toast({ content: this.$t('保存成功'), type: 'success' })
                // 传递新保存的模板名称给父组件
                this.$emit('confirmSuccess', this.formData.name)
                this.handleClose()
              } else {
                this.$toast({ content: res.message || this.$t('保存失败'), type: 'error' })
              }
            })
            .catch((error) => {
              console.error('保存搜索模板失败:', error)
              this.$toast({ content: this.$t('保存失败'), type: 'error' })
            })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.save-template-dialog {
  .dialog-content {
    padding: 20px;
    min-width: 400px;

    .mt-form-item {
      margin-bottom: 20px;

      .mt-input {
        width: 100%;
      }
    }
  }
}
</style>
