# CommonList 通用列表组件

## 概述

CommonList 是一个基于 ScTable 组件的通用列表组件，集成了搜索、表格展示、分页等功能，提供了完整的列表页面解决方案。

## Props 属性

### 搜索相关
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| enableSearch | Boolean | true | 是否启用搜索功能 |
| enableSearchTemplate | Boolean | true | 是否启用搜索模板 |
| enableSearchConditionSetting | Boolean | true | 是否启用搜索条件设置 |
| searchGridId | String | '' | 搜索网格ID |
| availableConditions | Array | [] | 可用搜索条件 |
| requiredConditions | Array | [] | 必需搜索条件 |
| searchFormRules | Object | {} | 搜索表单验证规则 |

### 表格相关
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| gridId | String | '' | 表格ID，用于列配置记忆 |
| columns | Array | [] | 表格列配置 |
| gridOptions | Object | {} | 表格其他配置选项 |
| toolbar | Array | [] | 工具栏按钮配置 |
| showRefreshButton | Boolean | false | 是否显示刷新按钮 |
| showRightButton | Boolean | true | 是否显示右侧按钮 |
| showColumnConfig | Boolean | true | 是否显示列配置 |

### 分页相关
| 属性名 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| enablePagination | Boolean | true | 是否启用分页 |
| pageSizes | Array | [20, 50, 200, 500, 1000] | 分页大小选项 |
| defaultPageSize | Number | 50 | 默认分页大小 |
| pagerLayouts | Array | ['PrevPage', 'JumpNumber', 'NextPage', 'FullJump', 'Sizes', 'Total'] | 分页布局配置 |

## Events 事件

| 事件名 | 参数 | 说明 |
|--------|------|------|
| search | { searchForm, page, pageSize } | 搜索事件 |
| reset | - | 重置事件 |
| templateSearch | template | 模板搜索事件 |
| searchConditionChange | { value, fieldName, item, searchForm } | 搜索条件变化事件 |
| toolbarClick | item | 工具栏按钮点击事件 |
| refresh | - | 刷新事件 |
| pageChange | { page, pageSize, searchForm } | 页码变化事件 |
| pageSizeChange | { page, pageSize, searchForm } | 页大小变化事件 |

## Methods 方法

### 基础方法
| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| setTableData | data: Array | - | 设置表格数据 |
| setLoading | status: Boolean | - | 设置加载状态 |
| setPagination | pagination: Object | - | 设置分页信息 |
| setSearchForm | form: Object | - | 设置搜索表单 |
| getSelectedRows | - | Array | 获取选中行数据 |
| getScTableRef | - | Object | 获取 ScTable 实例 |
| getGridRef | - | Object | 获取内部 vxe-grid 实例 |

### 高度计算和布局方法（新增）
| 方法名 | 参数 | 返回值 | 说明 |
|--------|------|--------|------|
| refreshTableLayout | - | - | 重新计算表格高度（推荐） |
| calculateAndSetTableHeight | - | - | 计算并设置表格高度（支持Tab环境） |
| recalculateScTableHeight | - | - | ScTable高度重新计算的核心方法 |
| forceRecalculateScTableHeight | - | - | 强制重新计算ScTable高度 |
| forceReRenderTable | - | - | 强制重渲染（最后备用方案） |

## 使用示例

### 基础用法

```vue
<template>
  <common-list
    ref="commonListRef"
    :columns="columns"
    :toolbar="toolbar"
    :available-conditions="searchConditions"
    grid-id="my-grid"
    @search="handleSearch"
    @toolbarClick="handleToolbarClick"
  >
    <!-- 自定义列插槽 -->
    <template #status="{ row }">
      <span>{{ row.status === 1 ? '启用' : '禁用' }}</span>
    </template>
  </common-list>
</template>

<script>
export default {
  data() {
    return {
      columns: [
        { type: 'checkbox', width: 60 },
        { field: 'name', title: '名称' },
        { field: 'status', title: '状态', slots: { default: 'status' } }
      ],
      toolbar: [
        { code: 'add', name: '新增', icon: 'vxe-icon-square-plus', status: 'primary' }
      ],
      searchConditions: [
        { field: 'name', label: '名称', type: 'input' }
      ]
    }
  },
  methods: {
    handleSearch(params) {
      // 处理搜索
    },
    handleToolbarClick(item) {
      // 处理工具栏点击
    }
  }
}
</script>
```

### 工具栏配置

工具栏按钮配置格式：
```javascript
const toolbar = [
  {
    code: 'add',           // 按钮编码
    name: '新增',          // 按钮名称
    icon: 'vxe-icon-square-plus', // 图标
    status: 'primary'      // 按钮状态
  }
]
```

### 搜索条件配置

搜索条件配置格式：
```javascript
const searchConditions = [
  {
    field: 'name',         // 字段名
    label: '名称',         // 标签
    type: 'input',         // 组件类型：input/select/number/date/dateRange
    options: 'statusOptions', // 选择框选项（仅select类型）
    props: {}              // 组件额外属性
  }
]
```

## 迁移指南

### 从旧版本迁移

1. **组件引用无需修改**：组件名称和基本用法保持不变
2. **新增可选属性**：可以选择性添加 ScTable 相关的新属性
3. **工具栏使用**：现在使用 `custom-tools` 插槽方式，功能更强大
4. **方法调用**：新增了 `getScTableRef()` 和 `getGridRef()` 方法

### 注意事项

1. 确保项目中已正确引入 ScTable 组件
2. 如果使用了 `gridId` 属性，将启用列配置记忆功能
3. 工具栏按钮现在通过插槽渲染，样式更统一
4. 保持了对原有 API 的完全兼容性
5. **国际化支持**：组件自动设置 vxe-pager 的中文显示，无需额外配置

## 国际化支持

### vxe-pager 中文化

组件内置了 VXETable 的中文国际化配置，自动将分页器的文本设置为中文：

- "共 X 条记录" 替代 "Total X records"
- "前往" 替代 "Go to"
- "页" 替代 "Page"
- "条/页" 替代 "items/page"
- "上一页/下一页" 等按钮文本

### 自定义国际化

如果需要自定义国际化文本，可以通过以下方式：

```javascript
import { setupVXETableI18n } from '@/components/CommonList/utils/vxe-i18n'

// 在应用启动时调用
setupVXETableI18n()
```

## 样式布局

### 重新设计的布局结构

组件采用优化的 Flexbox 布局，解决搜索区域动态高度变化问题：

```
.common-list (flex容器，高度100%，相对定位)
├── .common-list__search (搜索区域，动态高度，z-index:10)
├── .common-list__table (表格区域，flex:1，相对定位)
└── .common-list__pager (分页区域，固定高度56px，z-index:10)
```

### 核心优化特性

- **动态高度适配**：搜索区域展开/收起时表格自动重新计算高度
- **Tab环境兼容**：智能检测Tab组件，自动调整高度计算
- **分页器固定底部**：分页器始终固定在底部，不随表格内容滚动
- **表格自适应高度**：表格区域自动占据剩余空间
- **平滑过渡动画**：搜索区域状态变化时有平滑的过渡效果
- **无页面滚动条**：通过精确的高度控制避免页面滚动
- **响应式设计**：适配不同屏幕尺寸

### 技术实现

#### 1. 智能高度计算（支持Tab环境）
```javascript
// 计算并设置表格高度（自动检测Tab环境）
calculateAndSetTableHeight() {
  const viewportHeight = window.innerHeight
  const searchHeight = this.getSearchHeight()
  const buttonHeight = this.getButtonHeight()
  const pagerHeight = this.getPagerHeight()
  const otherHeight = this.getOtherElementsHeight() // 包含Tab高度检测

  const tableHeight = viewportHeight - searchHeight - buttonHeight - pagerHeight - otherHeight
  this.setTableHeight(tableHeight)
}

// Tab高度智能检测
getTabHeight() {
  // 1. DOM搜索：查找父级容器中的Tab元素
  // 2. Vue组件树搜索：遍历父组件查找Tab组件
  // 3. 上下文检测：通过路由等信息判断Tab环境
  // 4. 返回检测到的Tab高度或0
}
```

#### 2. 关键样式
```css
.common-list {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  position: relative;
}

.common-list__search {
  flex-shrink: 0;
  z-index: 10;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.common-list__table {
  flex: 1;
  overflow: hidden;
  min-height: 0;
  position: relative;
}

.common-list__pager {
  flex-shrink: 0;
  min-height: 56px;
  z-index: 10;
}
```

### 使用建议

1. **容器高度**：确保父容器有明确的高度设置
2. **避免嵌套滚动**：不要在已有滚动的容器中再次设置滚动
3. **合理分页**：根据屏幕高度设置合适的分页大小
4. **搜索条件数量**：建议控制搜索条件数量，避免搜索区域过高
5. **Tab环境使用**：在Tab页面中使用时，组件会自动检测并调整高度计算
6. **高度问题排查**：如遇高度计算问题，可查看控制台日志了解检测过程

## 完整示例

### 示例文件说明

- **`index.vue`** - 基本功能示例，展示组件的标准用法和高度计算
- **`CommonListTabsDemo.vue`** - Tab环境演示，展示组件在多Tab页面中的使用

### 推荐查看顺序

1. **`index.vue`** - 了解基本用法和高度计算特性
2. **`CommonListTabsDemo.vue`** - 了解Tab环境中的使用方式

### 在项目中使用

参考 `/src/views/Example/index.vue` 查看在实际页面中的使用方式。

## Tab环境兼容性（新增功能）

### 概述

组件新增了智能Tab环境检测功能，能够自动识别页面中的Tab组件并调整高度计算，确保在各种Tab环境中都能正确显示。

### 检测策略

#### 1. DOM树搜索
- 向上搜索父级容器中的Tab元素
- 支持多种Tab组件：`mt-tabs`、`vxe-tabs`、自定义Tab
- 验证找到的元素是否为真正的Tab组件

#### 2. Vue组件树搜索
- 遍历Vue组件树查找包含Tab的父组件
- 检查组件名称是否包含Tab相关特征
- 获取Tab组件的实际DOM高度

#### 3. 上下文检测
- 通过路由名称和路径判断Tab环境
- 检查页面URL中的Tab标识
- 提供默认Tab高度作为备选方案

### 使用方式

#### 自动检测（推荐）
```vue
<template>
  <div class="tab-container">
    <mt-tabs>
      <mt-tab-pane label="Tab 1">
        <common-list
          :columns="columns"
          :toolbar="toolbar"
          grid-id="tab1-grid"
        />
      </mt-tab-pane>
    </mt-tabs>
  </div>
</template>
```

组件会自动检测Tab环境并调整高度计算，无需额外配置。

#### 手动刷新
```javascript
// 如果Tab高度发生变化，可以手动触发重新计算
this.$refs.commonListRef.calculateAndSetTableHeight()
```

### 调试信息

组件提供详细的调试日志，帮助了解检测过程：

```
// 成功检测到Tab
在第1层找到有效Tab元素，高度: 48px
其他元素高度详情: { 页面头部: 60, Tab高度: 48, 页面边距: 40, 预留空间: 20, 总计: 168 }

// 未检测到Tab
未检测到Tab环境，Tab高度为0
其他元素高度详情: { 页面头部: 60, Tab高度: 0, 页面边距: 40, 预留空间: 20, 总计: 120 }
```

## 高度计算和布局优化指南

### 概述

组件提供了完整的高度计算和布局优化解决方案，包括：
- 智能Tab环境检测和高度适配
- ScTable高度重新计算（避免闪烁）
- 动态响应式布局调整
- 多种布局问题的解决方案

### 核心方法

#### 1. 智能高度计算（推荐）

```javascript
// 智能计算表格高度（支持Tab环境检测）
this.$refs.commonList.calculateAndSetTableHeight()

// 通用布局刷新方法
this.$refs.commonList.refreshTableLayout()
```

#### 2. ScTable专用方法

```javascript
// ScTable高度重新计算
this.$refs.commonList.recalculateScTableHeight()

// 强制重新计算（当常规方法失效时）
this.$refs.commonList.forceRecalculateScTableHeight()
```

#### 3. 最后备用方案

```javascript
// 强制重渲染（会产生闪烁，仅在必要时使用）
this.$refs.commonList.forceReRenderTable()
```

### 技术原理

#### ScTable高度重新计算的工作原理

1. **重写高度计算方法**：重写 ScTable 的 `getContainerHeight` 方法，防止使用 `calc(100vh)` 等问题样式
2. **强制重新计算容器高度**：重置 ScTable 的 `containerHeight` 属性并强制重新计算
3. **修正DOM结构和样式**：移除问题样式，设置正确的 flex 布局
4. **触发VXE Grid重新计算**：调用 VXE Grid 的内置方法进行布局刷新
5. **验证计算结果**：检查页面是否溢出和表格样式是否正确

#### 核心步骤

```javascript
// 1. 重写ScTable高度计算
this.overrideScTableHeightCalculation()

// 2. 强制重新计算容器高度
this.forceRecalculateContainerHeight()

// 3. 修正表格DOM结构
this.fixTableDOMStructure()

// 4. 触发VXE Grid重新计算
this.triggerVXEGridRecalculation()

// 5. 验证结果
this.validateHeightCalculation()
```

### 使用场景

#### 1. Tab环境中使用

```javascript
// 组件会自动检测Tab环境，无需额外配置
// 如需手动触发重新计算：
this.$refs.commonList.calculateAndSetTableHeight()
```

#### 2. 搜索栏展开/收起后

```javascript
// 组件内部已自动处理，如需手动触发：
this.$refs.commonList.refreshTableLayout()
```

#### 3. 窗口大小变化后

```javascript
// 组件内部已自动处理，如需手动触发：
this.$refs.commonList.calculateAndSetTableHeight()
```

#### 4. 动态修改表格配置后

```javascript
// 修改列配置后
this.columns = newColumns
await this.$nextTick()
this.$refs.commonList.refreshTableLayout()
```

#### 5. 表格布局出现问题时

```javascript
// 当表格高度计算出现问题时
this.$refs.commonList.forceRecalculateScTableHeight()
```

### 方法对比

| 方法 | 优点 | 缺点 | 使用场景 |
|------|------|------|----------|
| `calculateAndSetTableHeight()` | 智能检测Tab，精确计算 | - | Tab环境或复杂布局 |
| `refreshTableLayout()` | 简单易用，无闪烁 | - | 日常使用 |
| `recalculateScTableHeight()` | 专业精确，针对性强 | 稍微复杂 | ScTable高度问题 |
| `forceRecalculateScTableHeight()` | 解决复杂问题 | 性能开销稍大 | 严重布局问题 |
| `forceReRenderTable()` | 解决所有问题 | 明显闪烁 | 最后备用方案 |

### 最佳实践

#### 1. 根据场景选择合适的方法

```javascript
// ✅ Tab环境或复杂布局 - 智能检测
this.$refs.commonList.calculateAndSetTableHeight()

// ✅ 日常使用 - 简单有效
this.$refs.commonList.refreshTableLayout()

// ❌ 不推荐 - 除非必要
this.$refs.commonList.forceReRenderTable()
```

#### 2. 渐进式处理

```javascript
// 先尝试智能计算
this.$refs.commonList.calculateAndSetTableHeight()

// 如果问题仍然存在，使用ScTable专用方法
if (hasLayoutIssue) {
  this.$refs.commonList.forceRecalculateScTableHeight()
}
```

#### 3. 在合适的时机调用

```javascript
// 在DOM更新后调用
await this.$nextTick()
this.$refs.commonList.calculateAndSetTableHeight()

// 在数据变化后调用
this.$refs.commonList.setTableData(newData)
this.$refs.commonList.refreshTableLayout()

// Tab切换后调用
this.$refs.commonList.calculateAndSetTableHeight()
```

### 迁移指南

如果您之前使用了强制重渲染方法，可以按以下方式迁移：

```javascript
// 旧方式（会闪烁）
this.$refs.commonList.forceReRenderTable()

// 新方式（智能检测，推荐）
this.$refs.commonList.calculateAndSetTableHeight()

// 或者通用方式（无闪烁）
this.$refs.commonList.refreshTableLayout()

// 或者ScTable专用方式
this.$refs.commonList.recalculateScTableHeight()
```

## 总结

CommonList 组件经过持续优化，现在提供了：

1. **智能Tab环境检测** - 自动适配各种Tab布局
2. **精确高度计算** - 支持复杂页面布局
3. **无闪烁布局刷新** - 提供多种布局优化方案
4. **完整的国际化支持** - 内置中文化配置
5. **丰富的API接口** - 满足各种使用场景
6. **详细的调试信息** - 便于问题排查和优化

组件设计遵循渐进增强原则，保持向后兼容的同时提供了更强大的功能。无论是简单的列表页面还是复杂的Tab环境，都能提供优秀的用户体验。
