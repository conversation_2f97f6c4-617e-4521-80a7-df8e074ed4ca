const Router = [
  {
    path: 'source-files',
    name: 'source-files',
    component: () => import('@/views/sourceFiles/index'),
    meta: { title: '货源档案列表' }
  },
  // {
  //   path: 'source-files-add',
  //   name: 'source-files-add',
  //   component: () => import('@/views/sourceFiles/components/add.vue'),
  //   meta: { title: '新增货源档案列表' },
  // },
  {
    path: 'source-files-add',
    name: 'source-files-add',
    component: () => import('@/views/sourceFiles/components/addNew.vue'),
    meta: { title: '新增货源档案列表' }
  },
  {
    path: 'exemption-application',
    name: 'exemption-application',
    component: () => import('@/views/exemption/index'),
    meta: { title: '豁免申请单' }
  },
  {
    path: 'category-configuration',
    name: 'category-configuration',
    component: () => import('@/views/categoryConfig/index'),
    meta: { title: '样品确认品类配置' }
  },
  {
    path: 'sample-query',
    name: 'sample-query',
    component: () => import('@/views/sampleQuery/index'),
    meta: { title: '样品确认单查询' }
  },
  {
    path: 'sample-process',
    name: 'sample-process',
    component: () => import('@/views/sampleProcess/index'),
    meta: { title: '样品确认单过程查询' }
  }
]
export default Router
