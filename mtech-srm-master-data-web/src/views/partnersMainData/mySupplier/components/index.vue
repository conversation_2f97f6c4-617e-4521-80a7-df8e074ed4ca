<template>
  <div class="supplier-effective-apply">
    <div class="detail-header--wrap">
      <p class="detail-header-name">
        <!-- {{ effectiveData.supplierEnterpriseName }} -->

        <span class="detail-header-button--wrap">
          <mt-button type="text" class="detail-header-button" @click="backDetail">{{
            $t('返回')
          }}</mt-button>
        </span>
      </p>
      <p class="detail-header-category">
        <!-- {{ $t('品类：') }}{{ effectiveData.categoryName }} -->
      </p>
      <p class="detail-header-items">
        <span class="detail-header-item">
          <!-- {{ $t('公司：') }}{{ effectiveData.orgName }} -->
        </span>
        <span class="detail-header-item">
          <!-- {{ $t('生效类型：')
          }}{{
            effectiveData.effectiveType === '1' ? $t('预生效（SAP）') : $t('正式生效（SRM）')
          }} -->
        </span>
        <span class="detail-header-item">
          <!-- {{ $t('引入场景：') }}{{ effectiveData.sceneDefineName }} -->
        </span>
      </p>
    </div>
    <div class="detail-content--wrap">
      <mt-form
        ref="effectiveOrgDTO"
        class="detail-effectiveorg--form"
        :model="effectiveOrgData"
        :rules="rules"
      >
        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="supplierCharacter" :label="$t('供应商特性')">
              <mt-select
                v-model="effectiveOrgData.supplierCharacter"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, supplierCharacterList, 'text')"
                :data-source="supplierCharacterList"
                :placeholder="$t('')"
                :disabled="isEdit"
                @change="supplierCharacterChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6" v-if="['2M01', '2S06'].includes(effectiveOrgData.organizationCode)">
            <mt-form-item prop="multiplePayCondition" :label="$t('是否多种付款条件')">
              <mt-radio
                v-model="effectiveOrgData.multiplePayCondition"
                :data-source="multiplePayConditionList"
                disabled
              />
            </mt-form-item>
          </mt-col>

          <mt-col :span="6" v-if="effectiveOrgData.supplierCharacter === '1'">
            <mt-form-item prop="tradePartnerCode" :label="$t('贸易伙伴')">
              <!-- <mt-select
                v-model="effectiveOrgData.tradePartnerCode"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, tradePartnerList, 'tradingPartnerName')"
                :data-source="tradePartnerList"
                :fields="{
                  value: 'tradingPartnerCode',
                  text: 'tradingPartnerName',
                }"
                :placeholder="$t('')"
                :disabled="isEdit"
                @change="tradePartnerChange"
              ></mt-select> -->
              <mt-input
                :disabled="isEdit"
                v-model="effectiveOrgData.tradePartnerName"
                :placeholder="$t('')"
              />
            </mt-form-item>
          </mt-col>

          <mt-col :span="6" v-if="effectiveOrgData.supplierCharacter === '1'">
            <mt-form-item prop="sapCode" :label="$t('SAP编码')">
              <mt-input
                :disabled="isEdit"
                v-model="effectiveOrgData.sapCode"
                :placeholder="$t('')"
              />
            </mt-form-item>
          </mt-col>
        </mt-row>

        <mt-row :gutter="24">
          <mt-col :span="6">
            <mt-form-item prop="payConditionCode" :label="$t('付款条件')">
              <!-- <mt-select
                v-model="effectiveOrgData.paymentTerm"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, paymentTermList, 'paymentTermsName')"
                :data-source="paymentTermList"
                :fields="{
                  value: 'paymentTermsCode',
                  text: 'paymentTermsName',
                }"
                :disabled="isEdit"
                :placeholder="$t('请选择付款条件')"
                @change="paymentTermsChange"
              ></mt-select> -->
              <mt-input
                :disabled="isEdit"
                v-model="effectiveOrgData.payConditionName"
                :placeholder="$t('')"
              ></mt-input>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="accountChartCode" :label="$t('统驭科目')">
              <!-- <mt-select
                v-model="effectiveOrgData.subject"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, subjectList, 'itemName')"
                :data-source="subjectList"
                :fields="dictFields"
                :placeholder="$t('请选择统驭科目')"
                :disabled="isEdit"
                @change="subjectChange"
              ></mt-select> -->
              <mt-input
                :disabled="isEdit"
                v-model="effectiveOrgData.accountChartCode"
                :placeholder="$t('')"
              ></mt-input>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="cashGroupCode" :label="$t('现金管理组')">
              <!-- <mt-select
                v-model="effectiveOrgData.cashGroup"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, cashGroupList, 'itemName')"
                :data-source="cashGroupList"
                :fields="dictFields"
                :placeholder="$t('请选择现金管理组')"
                :disabled="isEdit"
                @change="cashGroupChange"
              ></mt-select> -->
              <mt-input
                :disabled="isEdit"
                v-model="effectiveOrgData.cashGroupCode"
                :placeholder="$t('')"
              ></mt-input>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="accountGroup" :label="$t('账户组')">
              <mt-select
                v-model="effectiveOrgData.accountGroup"
                float-label-type="Never"
                :allow-filtering="true"
                :show-clear-button="true"
                :filtering="(e) => filteringResource(e, accountGroupList, 'itemName')"
                :data-source="accountGroupList"
                :fields="dictFields"
                :placeholder="$t('')"
                :disabled="isEdit"
                @change="accountGroupChange"
              ></mt-select>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="payTypeCode" :label="$t('付款方式')">
              <!-- <mt-select
                v-model="effectiveOrgData.paymentMode"
                :show-clear-button="true"
                float-label-type="Never"
                :data-source="paymentModeList"
                :fields="{
                  value: 'paymethodCode',
                  text: 'paymethodName',
                }"
                :placeholder="$t('请选择付款方式')"
                :disabled="isEdit"
                @change="paymentModeChange"
              ></mt-select> -->

              <mt-input
                :disabled="isEdit"
                v-model="effectiveOrgData.payTypeCodeName"
                :placeholder="$t('')"
              ></mt-input>
            </mt-form-item>
          </mt-col>

          <mt-col :span="6">
            <mt-form-item prop="subRange" :label="$t('子范围')">
              <!-- <mt-multi-select
                v-model="effectiveOrgData.subRange"
                float-label-type="Never"
                :allow-filtering="true"
                :filtering="(e) => filteringResource(e, subRangeList, 'itemName')"
                :data-source="subRangeList"
                :fields="dictFields"
                :placeholder="$t('')"
                :disabled="isEdit"
                @change="subRangeChange"
              ></mt-multi-select> -->
              <mt-input
                :disabled="isEdit"
                v-model="effectiveOrgData.subRangeName"
                :placeholder="$t('')"
              ></mt-input>
            </mt-form-item>
          </mt-col>
        </mt-row>
      </mt-form>
      <mt-data-grid
        :data-source="gridDataSource"
        :column-data="gridColumnData"
        ref="dataGrid"
        height="200"
      ></mt-data-grid>
    </div>
  </div>
</template>
<script>
import { gridColumnData } from './detailGrid.js'

export default {
  data() {
    return {
      //供应商特性
      supplierCharacterList: [
        { value: '1', text: this.$t('关联供应商') },
        { value: '2', text: this.$t('非关联供应商') }
      ],
      multiplePayConditionList: [
        { value: 1, label: this.$t('是'), text: this.$t('是') },
        { value: 0, label: this.$t('否'), text: this.$t('否') }
      ],
      tradePartnerList: [], // 贸易伙伴列表
      paymentTermList: [], // 付款条件列表
      subjectList: [], // 统驭科目列表
      cashGroupList: [], // 现金管理组
      accountGroupList: [], // 账户组列表
      paymentModeList: [], // 付款方式列表
      subRangeList: [], // 子范围列表
      effectiveOrgData: {}, //对象集合
      isEdit: true,
      effectiveData: {},
      dictFields: {
        value: 'itemCode',
        text: 'itemName'
      },

      // rules: {
      //   supplierCharacter: [
      //     {
      //       required: true,
      //       message: this.$t('请选择供应商特性'),
      //       trigger: 'blur',
      //     },
      //   ],
      //   tradePartnerCode: [
      //     {
      //       required: true,
      //       message: this.$t('请选择贸易伙伴'),
      //       trigger: 'blur',
      //     },
      //     {
      //       validator: this.validateTradePartner,
      //       trigger: 'blur',
      //     },
      //   ],
      //   sapCode: [
      //     {
      //       required: true,
      //       message: this.$t('请输入SAP编码'),
      //       trigger: 'blur',
      //     },
      //     {
      //       validator: this.validateSapCode,
      //       trigger: 'blur',
      //     },
      //   ],
      //   paymentTerm: [
      //     {
      //       required: true,
      //       message: this.$t('请选择付款条件'),
      //       trigger: 'blur',
      //     },
      //   ],
      //   subject: [
      //     {
      //       required: true,
      //       message: this.$t('请选择统驭科目'),
      //       trigger: 'blur',
      //     },
      //   ],
      //   cashGroup: [
      //     {
      //       required: true,
      //       message: this.$t('请选择现金管理组'),
      //       trigger: 'blur',
      //     },
      //   ],
      //   accountGroup: [{ required: true, message: this.$t('请选择账户组'), trigger: 'blur' }],
      //   paymentMode: [
      //     {
      //       required: true,
      //       message: this.$t('请选择付款方式'),
      //       trigger: 'blur',
      //     },
      //   ],
      //   subRange: [
      //     {
      //       required: true,
      //       message: this.$t('请选择子范围'),
      //       trigger: 'blur',
      //     },
      //     { validator: this.validateSubRange, trigger: 'blur' },
      //   ],
      // },
      gridDataSource: [],
      gridColumnData: gridColumnData,
      isDocumentNotEdit: false,
      paymentTermsList: []
    }
  },
  computed: {
    partnerCode() {
      return this.$route.query.partnerCode
    },
    orgCode() {
      return this.$route.query.orgCode
    }
  },
  async created() {
    await this.getAccountGroupList()
    this.detail()
  },
  methods: {
    async getAccountGroupList() {
      let params = {
        dictCode: 'ACCOUNT_GROUP',
        nameLike: ''
      }
      if (['2M01', '2S06'].includes(this.orgCode)) {
        params.dictCode = 'ACCOUNT_GROUP_TX'
      }
      this.$API.sourceLists.queryDict(params).then((res) => {
        this.accountGroupList = res.data
      })
    },
    async getPaymentTerms() {
      let params = {
        page: {
          current: 1,
          size: 200
        }
      }
      this.$API.partnersMainData.pagePaymentTermsApi(params).then((res) => {
        if (res.code === 200) {
          this.paymentTermsList = res.data.records || []
        }
      })
    },
    async detail() {
      await this.getPaymentTerms()
      let params = {
        partnerCode: this.partnerCode,
        orgCode: this.orgCode
      }
      await this.$API.partnersMainData.detail(params).then((res) => {
        this.effectiveOrgData = { ...res.data.businessPartnerAccountingDTO }
        this.gridDataSource = res.data.businessPartnerPurchasingDTOList?.map((item) => {
          this.paymentTermsList.forEach((v) => {
            if (v.paymentTermsCode === item.payConditionCode) {
              item.payConditionName = v.paymentTermsName
            }
          })
          return item
        })
      })
    },
    // 模糊搜索，不区分大小写模糊搜索
    filteringResource(e, dataSource, key) {
      if (typeof e.text === 'string' && e.text) {
        e.updateData(
          dataSource?.filter((f) => f[key]?.toUpperCase().includes(e?.text?.toUpperCase()))
        )
      } else {
        e.updateData(dataSource)
      }
    },
    //供应商特性
    supplierCharacterChange() {
      console.log(this.effectiveOrgData)
      // const { itemData } = event
      // if (itemData.value === '1') {
      //   // 关联供应商
      // }
    },
    //贸易伙伴
    tradePartnerChange(event) {
      const { itemData } = event
      console.log(this.effectiveOrgData, itemData)
      if (itemData) {
        this.effectiveOrgData.tradePartnerCode = itemData.tradingPartnerCode
        this.effectiveOrgData.tradePartnerName = itemData.tradingPartnerName
      } else {
        this.effectiveOrgData.tradePartnerCode = ''
        this.effectiveOrgData.tradePartnerName = ''
      }
    },
    //付款条件
    paymentTermsChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.paymentTermsName = itemData.paymentTermsName
      } else {
        this.effectiveOrgData.paymentTermsName = ''
      }
    },
    //统驭科目
    subjectChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.subjectName = itemData.itemName
      } else {
        this.effectiveOrgData.subjectName = ''
      }
    },
    //现金管理组
    cashGroupChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.cashGroupName = itemData.itemName
      } else {
        this.effectiveOrgData.cashGroupName = ''
      }
    },
    //账户组
    accountGroupChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.accountGroupName = itemData.itemName
      } else {
        this.effectiveOrgData.accountGroupName = ''
      }
    },
    //付款方式
    paymentModeChange(event) {
      const { itemData } = event
      if (itemData) {
        this.effectiveOrgData.paymentModeName = itemData.itemName
      } else {
        this.effectiveOrgData.paymentModeName = ''
      }
    },
    //子范围
    subRangeChange(event) {
      const { value } = event
      this.$set(this.effectiveOrgData, 'subRange', value)
    },

    // validateSubRange(rule, value, callback) {
    //   if (Array.isArray(value) && value.length < 1) {
    //     callback(new Error('请选择子范围'))
    //   } else {
    //     callback()
    //   }
    // },
    // validateTradePartner(rule, value, callback) {
    //   if (this.effectiveData.subRange === '1') {
    //     if (!value) {
    //       callback(this.$t('请选择贸易伙伴'))
    //     } else {
    //       callback()
    //     }
    //   } else {
    //     callback()
    //   }
    // },
    // validateSapCode(rule, value, callback) {
    //   if (this.effectiveData.subRange === '1') {
    //     if (Array.isArray(value) && value.length < 1) {
    //       callback(this.$t('请输入SAP编码'))
    //     } else {
    //       callback()
    //     }
    //   } else {
    //     callback()
    //   }
    // },

    backDetail() {
      this.$router.go(-1)
    }
  }
}
</script>
<style lang="scss" scoped>
.supplier-effective-apply {
  background: #fafafa;
  height: 100%;

  .detail-header--wrap {
    background: rgba(99, 134, 193, 0.08);
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 8px;
    box-shadow: 0 0 10px 0 rgba(137, 120, 120, 0.06);
    padding: 20px 30px;
    margin-bottom: 16px;

    .detail-header-name {
      font-size: 20px;
      line-height: 32px;
      font-weight: 600;
      color: rgba(41, 41, 41, 1);
    }
    .detail-header-category {
      font-size: 12px;
      line-height: 16px;
      color: rgba(41, 41, 41, 1);
    }
    .detail-header-items {
      font-size: 14px;
      font-weight: 600;
      margin-top: 20px;
      color: rgba(41, 41, 41, 1);
      .detail-header-item {
        margin-right: 24px;
      }
    }
  }

  .detail-content--wrap {
    padding: 24px;
    background: rgba(255, 255, 255, 1);
    border-radius: 8px 8px 0 0 0;
    box-shadow: 0 0 8px 0 rgba(0, 0, 0, 0.06);
  }

  .detail-effectiveorg--form {
    padding-bottom: 24px;
    border-bottom: 1px solid #e8e8e8;
    margin-bottom: 40px;
  }

  .effective-form-item--width {
  }

  .detail-header-button--wrap {
    float: right;
    .detail-header-button {
      margin-right: 24px;
    }
  }

  .detail-href--item {
    color: #00469c;
    margin-top: 12px;
    display: block;
    cursor: pointer;
  }

  /deep/.e-multiselect.e-checkbox .e-multi-select-wrapper .e-searcher {
    width: 100%;
  }
}
</style>
