<!-- 附件管理弹框 -->
<template>
  <mt-dialog ref="fileDialog" height="500" :header="header" :buttons="buttons" @beforeClose="cancel">
    <div class="dialog-content">
      <sc-table
        ref="fileTable"
        :columns="columns"
        :table-data="tableData"
        :loading="loading"
        :fix-height="300"
      >
        <template slot="custom-tools">
          <vxe-button
            v-for="item in toolbar"
            :key="item.code"
            :status="item.status"
            :icon="item.icon"
            :loading="item.loading"
            size="small"
            @click="handleClickToolBar(item)"
          >
            {{ item.name }}
          </vxe-button>
        </template>
        <!-- 附件名称列 -->
        <template #fileNameDefault="{ row }">
          <span
            style="cursor: pointer; color: #2783fe; text-decoration: underline;"
            @click="handlePreview(row)"
          >
            {{ row.fileName }}
          </span>
        </template>
        <!-- 测试结论列 -->
        <template #testResultDefault="{ row }">
          <span :class="row.testResult === '测试合格' ? 'test-pass' : 'test-fail'">
            {{ row.testResult }}
          </span>
        </template>
        <template #operateDefault="{ row }">
          <span style="color: #2783fe; cursor: pointer" @click="handleDownload(row)">
            {{ $t('下载') }}
          </span>
        </template>
      </sc-table>
    </div>
  </mt-dialog>
</template>

<script>
import ScTable from '@/components/ScTable/src/index'
import { download } from '@/utils/util'

export default {
  name: 'AttachmentDialog',
  components: {
    ScTable
  },
  props: {
    // 传入的数据
    modalData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      tableData: [],
      toolbar: [],
      // 弹框按钮配置
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      // 表格列配置
      columns: [
        {
          field: 'fileName',
          title: this.$t('文件名称'),
          minWidth: 200,
          slots: { default: 'fileNameDefault' }
        },
        {
          field: 'sampTimes',
          title: this.$t('第几次送样'),
          minWidth: 180
        },
        {
          field: 'planStartTimeStr',
          title: this.$t('计划开始时间'),
          minWidth: 180
        },
        {
          field: 'planEndTimeStr',
          title: this.$t('计划完成时间'),
          minWidth: 180
        },
        {
          field: 'actualStartTimeStr',
          title: this.$t('实际开始时间'),
          minWidth: 180
        },
        {
          field: 'actualEndTimeStr',
          title: this.$t('实际完成时间'),
          minWidth: 180
        },
        {
          field: 'testResult',
          title: this.$t('测试结论'),
          minWidth: 120,
          slots: { default: 'testResultDefault' }
        },
        {
          field: 'operate',
          title: this.$t('操作'),
          slots: {
            default: 'operateDefault'
          }
        }
      ]
    }
  },
  computed: {
    header() {
      return this.modalData.title || '附件列表'
    }
  },
  mounted() {
    // 设置表格数据
    this.tableData = this.modalData.attachmentList || []
    // 显示弹框
    this.$refs.fileDialog.ejsRef.show()
  },
  methods: {
    handleClickToolBar() {},
    handlePreview(row) {
      let params = {
        id: row?.fileId || row.id,
        useType: 2
      }
      this.$API.fileService.getMtPreview(params).then((res) => {
        window.open(res.data)
      })
    },
    handleDownload(row) {
      this.$loading()
      this.$API.fileService.downloadPrivateFile({ id: row?.fileId || row.id }).then((res) => {
        download({
          fileName: row.fileName,
          blob: new Blob([res.data])
        })
      }).finally(() => {
        this.$hloading()
      })
    },
    // 关闭弹框 - 兼容 $dialog 系统
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style scoped>
.dialog-content {
  margin: 16px 0;
}

.test-pass {
  color: #67c23a;
  font-weight: bold;
}

.test-fail {
  color: #f56c6c;
  font-weight: bold;
}
</style>
