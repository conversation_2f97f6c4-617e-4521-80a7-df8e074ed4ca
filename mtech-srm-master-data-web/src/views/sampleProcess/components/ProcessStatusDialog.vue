<!-- 流程状态弹框 -->
<template>
  <mt-dialog
    ref="processDialog"
    height="660"
    width="1200"
    :header="header"
    :buttons="buttons"
    @beforeClose="cancel"
  >
    <div class="dialog-content">
      <!-- 流程图展示区域 -->
      <div class="process-flow-container">
        <div class="process-header">
          <div class="process-info">
            <span class="process-label">{{ $t('样品确认单号') }}：</span>
            <span class="process-value">{{ processData.sampleNo || '' }}</span>
            <span class="process-label ml-20">{{ $t('样品名称') }}：</span>
            <span class="process-value">{{ processData.sampleName || '' }}</span>
          </div>
        </div>
        <!-- 流程步骤 -->
        <div class="process-steps-container">
          <div
            v-for="(row, rowIndex) in processStepsRows"
            :key="rowIndex"
            class="process-steps-row"
          >
            <div
              v-for="(step, index) in row"
              :key="step.originalIndex"
              class="process-step"
              :class="{ active: step.isActive, completed: step.isCompleted }"
            >
              <!-- 步骤图标 -->
              <div class="step-icon">
                <div class="icon-circle" :class="{ red: step.isActive }">
                  <span v-if="step.isActive">{{ step.originalIndex + 1 }}</span>
                  <span v-else-if="step.isCompleted">✓</span>
                  <span v-else>{{ step.originalIndex + 1 }}</span>
                </div>
              </div>
              <!-- 步骤内容 -->
              <div class="step-content">
                <div class="step-title">{{ step.processStatusDesc }}</div>
                <div class="step-details">
                  <div class="step-time">{{ $t('时间') }}：{{ step.optTimeStr }}</div>
                  <div class="step-operator">{{ $t('操作人') }}：{{ step.operator }}</div>
                  <div v-if="step.planStartTimeStr" class="step-planned">
                    {{ $t('计划开始时间') }}：{{ step.planStartTimeStr }}
                  </div>
                  <div v-if="step.planEndTimeStr" class="step-planned">
                    {{ $t('计划完成时间') }}：{{ step.planEndTimeStr }}
                  </div>
                  <div v-if="step.actualStartTimeStr" class="step-planned">
                    {{ $t('实际开始时间') }}：{{ step.actualStartTimeStr }}
                  </div>
                  <div v-if="step.actualEndTimeStr" class="step-planned">
                    {{ $t('实际完成时间') }}：{{ step.actualEndTimeStr }}
                  </div>
                </div>
              </div>
              <!-- 连接线 -->
              <div v-if="!isLastStepInRow(rowIndex, index)" class="step-connector">
                <div class="connector-line" :class="{ active: isConnectorActive(rowIndex, index) }"></div>
              </div>
              <!-- 换行连接线 -->
              <!-- <div v-if="isLastStepInRow(rowIndex, index) && rowIndex < processStepsRows.length - 1" class="row-connector">
                <div class="row-connector-line" :class="{ active: isRowConnectorActive(rowIndex, index) }"></div>
              </div> -->
            </div>
          </div>
        </div>
        <!-- 流程说明 -->
        <div class="process-note">
          <span class="note-text">流程说明：当前已完成的流程为第{{ currentActiveStepIndex + 1 }}步</span>
        </div>
      </div>
    </div>
  </mt-dialog>
</template>

<script>
import { processStatusOptions } from '../config/index'
export default {
  name: 'ProcessStatusDialog',
  props: {
    // 传入的数据
    modalData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      // 弹框按钮配置
      buttons: [
        {
          click: this.cancel,
          buttonModel: { content: this.$t('关闭') }
        }
      ],
      // 流程数据
      processData: {},
      // 流程步骤
      processSteps: [],

      processStatusOptions
    }
  },
  computed: {
    header() {
      return this.modalData.title || this.$t('样品流程状态')
    },
    steps() {
      return this.processStatusOptions.findIndex((item) => item.value === this.processData.processStatus)
    },
    // 将流程步骤按每行6个进行分组
    processStepsRows() {
      const stepsPerRow = 6
      const rows = []

      // 为每个步骤添加原始索引
      const stepsWithIndex = this.processSteps.map((step, index) => ({
        ...step,
        originalIndex: index
      }))

      for (let i = 0; i < stepsWithIndex.length; i += stepsPerRow) {
        rows.push(stepsWithIndex.slice(i, i + stepsPerRow))
      }

      return rows
    },
    // 获取当前活跃步骤的索引
    currentActiveStepIndex() {
      const activeStepIndex = this.processSteps.findIndex(step => step.isActive)
      return activeStepIndex >= 0 ? activeStepIndex : this.steps
    }
  },
  mounted() {
    // 设置流程数据
    this.processData = this.modalData.processData || {}
    this.initProcessSteps()
    // 显示弹框
    this.$refs.processDialog.ejsRef.show()
  },
  methods: {
    // 初始化流程步骤
    initProcessSteps() {
      this.$API.sourceFiles.statusListSampleProcessApi({ id: this.processData.id }).then((res) => {
        if (res.code === 200) {
          // 将所有步骤都设置为已完成状态
          this.processSteps = res.data.map((item) => {
            return {
              ...item,
              isActive: false,
              isCompleted: true
            }
          })
        }
      })
    },
    // 判断是否为行内最后一个步骤
    isLastStepInRow(rowIndex, stepIndex) {
      const currentRow = this.processStepsRows[rowIndex]
      return stepIndex === currentRow.length - 1
    },
    // 判断水平连接线是否应该激活
    isConnectorActive(rowIndex, stepIndex) {
      const currentStep = this.processStepsRows[rowIndex][stepIndex]
      const nextStep = this.processStepsRows[rowIndex][stepIndex + 1]

      // 如果当前步骤已完成，且下一个步骤存在，则连接线激活
      return currentStep && currentStep.isCompleted && nextStep
    },
    // 判断换行连接线是否应该激活
    isRowConnectorActive(rowIndex, stepIndex) {
      const currentStep = this.processStepsRows[rowIndex][stepIndex]
      const nextRowFirstStep = this.processStepsRows[rowIndex + 1] && this.processStepsRows[rowIndex + 1][0]

      // 如果当前步骤已完成，且下一行第一个步骤存在，则换行连接线激活
      return currentStep && currentStep.isCompleted && nextRowFirstStep
    },
    // 关闭弹框 - 兼容 $dialog 系统
    cancel() {
      this.$emit('cancel-function')
    }
  }
}
</script>

<style scoped>
.dialog-content {
  padding: 0;
}

.process-flow-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 20px;
}

.process-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 15px;
  border-bottom: 1px solid #e9ecef;
}

.process-info {
  flex: 1;
  font-size: 14px;
}

.process-label {
  color: #666;
  margin-right: 5px;
}

.process-value {
  color: #333;
  font-weight: 500;
}

.ml-20 {
  margin-left: 20px;
}

.process-steps-container {
  margin: 30px 0;
}

.process-steps-row {
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  margin-bottom: 40px;
  position: relative;
}

.process-steps-row:last-child {
  margin-bottom: 0;
}

.process-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  flex: 1;
  max-width: calc(100% / 6);
  min-width: 150px;
  position: relative;
  margin-right: 10px;
}

.process-step:last-child {
  margin-right: 0;
}

.step-icon {
  margin-bottom: 10px;
  z-index: 2;
}

.icon-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #e9ecef;
  border: 2px solid #dee2e6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  color: #6c757d;
}

.icon-circle.red {
  background: #dc3545;
  border-color: #dc3545;
  color: white;
}

.process-step.completed .icon-circle {
  background: #28a745;
  border-color: #28a745;
  color: white;
}

.step-content {
  text-align: center;
  background: white;
  border: 1px solid #e9ecef;
  border-radius: 4px;
  padding: 10px;
  min-width: 120px;
  max-width: 140px;
  width: 100%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  word-wrap: break-word;
}

.step-title {
  font-weight: bold;
  color: #333;
  margin-bottom: 8px;
  font-size: 14px;
}

.step-details {
  font-size: 12px;
  color: #666;
  line-height: 1.4;
}

.step-time,
.step-operator,
.step-planned {
  margin-bottom: 2px;
}

.step-connector {
  position: absolute;
  top: 20px;
  left: calc(50% + 5px);
  width: calc(100% + 10px);
  height: 2px;
  z-index: 1;
}

.connector-line {
  height: 2px;
  background: #dee2e6;
  margin: 0 5px;
}

.connector-line.active {
  background: #dc3545;
}

/* 换行连接线样式 */
.row-connector {
  position: absolute;
  top: 40px;
  left: 50%;
  transform: translateX(-50%);
  width: 2px;
  height: 40px;
  z-index: 1;
}

.row-connector-line {
  width: 2px;
  height: 100%;
  background: #dee2e6;
}

.row-connector-line.active {
  background: #dc3545;
}

.process-note {
  background: #d1ecf1;
  border: 1px solid #bee5eb;
  border-radius: 4px;
  padding: 10px;
  margin-top: 20px;
}

.note-text {
  color: #0c5460;
  font-size: 14px;
}
</style>
