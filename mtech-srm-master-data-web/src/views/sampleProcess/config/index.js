import { i18n } from '@/main.js'

// 工具栏按钮配置
export const toolbar = [
  {
    code: 'export',
    name: i18n.t('导出'),
    icon: '',
    status: 'info',
    loading: false
  }
]

export const processStatusOptions = [
  { text: i18n.t('采购启动'), value: 1 },
  { text: i18n.t('技术确认'), value: 2 },
  { text: i18n.t('资料审核'), value: 3 },
  { text: i18n.t('测试中'), value: 4 },
  { text: i18n.t('封样'), value: 5 },
  { text: i18n.t('结束'), value: 6 }
]

export const columnData = [
  {
    type: 'checkbox',
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    type: 'seq',
    title: i18n.t('序号'),
    width: 50,
    fixed: 'left',
    align: 'center'
  },
  {
    title: i18n.t('样品确认单号'),
    field: 'sampleNo',
    minWidth: 160
  },
  {
    title: i18n.t('样品编码'),
    field: 'sampleCode',
    minWidth: 120
  },
  {
    title: i18n.t('样品名称'),
    field: 'sampleName',
    minWidth: 120
  },
  {
    title: i18n.t('公司编码'),
    field: 'companyCode',
    minWidth: 120
  },
  {
    title: i18n.t('公司名称'),
    field: 'companyName',
    minWidth: 200
  },
  {
    title: i18n.t('工厂编码'),
    field: 'siteCode',
    minWidth: 120
  },
  {
    title: i18n.t('工厂名称'),
    field: 'siteName',
    minWidth: 200
  },
  {
    title: i18n.t('供应商编码'),
    field: 'supplierCode',
    minWidth: 120
  },
  {
    title: i18n.t('供应商名称'),
    field: 'supplierName',
    minWidth: 200
  },
  {
    title: i18n.t('送样日期'),
    field: 'sendSampleDateStr',
    minWidth: 160
  },
  {
    title: i18n.t('样品结论'),
    field: 'sampleConclusion'
  },
  {
    title: i18n.t('测试报告'),
    field: 'testReport',
    minWidth: 120,
    slots: {
      default: 'testReportDefault'
    }
  },
  {
    title: i18n.t('当前流程状态'),
    field: 'processStatusDesc',
    minWidth: 120,
    slots: {
      default: 'processStatusDefault'
    }
  },
  {
    title: i18n.t('创建人'),
    field: 'plmCreateUserName'
  },
  {
    title: i18n.t('创建时间'),
    field: 'plmCreateTimeStr',
    minWidth: 160
  },
  {
    title: i18n.t('更新人'),
    field: 'plmUpdateUserName'
  },
  {
    title: i18n.t('更新时间'),
    field: 'plmUpdateTimeStr',
    minWidth: 160
  },
  {
    title: i18n.t('流程说明'),
    field: 'processDesc',
    minWidth: 200
  }
]
