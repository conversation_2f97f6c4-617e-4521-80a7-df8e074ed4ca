/**
 * 样品确认单过程查询搜索表单配置
 */
import { i18n } from '@/main.js'
import { processStatusOptions } from './index'

export const getSearchFormItems = () => [
  {
    label: i18n.t('样品确认单号'),
    field: 'sampleNo',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入样品确认单号')
    }
  },
  {
    label: i18n.t('样品编码'),
    field: 'sampleCode',
    type: 'input',
    props: {
      placeholder: i18n.t('支持多个精准查询(用空格隔开)')
    }
  },
  {
    label: i18n.t('样品名称'),
    field: 'sampleName',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入样品名称')
    }
  },
  {
    label: i18n.t('公司'),
    field: 'companyCode',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/company/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      fields: { text: 'companyName', value: 'companyCode' },
      searchFields: ['companyName', 'companyCode']
    }
  },
  {
    label: i18n.t('工厂'),
    field: 'siteCode',
    type: 'remoteAutocomplete',
    props: {
      url: `/masterDataManagement/tenant/site/paged-query?BU_CODE=${localStorage.getItem(
        'currentBu'
      )}`,
      fields: { text: 'siteName', value: 'siteCode' },
      searchFields: ['siteName', 'siteCode']
    }
  },
  {
    label: i18n.t('供应商'),
    field: 'supplierCode',
    type: 'remoteAutocomplete',
    props: {
      url: '/masterDataManagement/tenant/supplier/paged-query-distinct-no-scope',
      fields: { text: 'supplierName', value: 'supplierCode' },
      searchFields: ['supplierName', 'supplierCode']
    }
  },
  {
    label: i18n.t('送样日期'),
    field: 'sendSampleDate',
    type: 'dateRange',
    props: {
      placeholder: i18n.t('请选择送样日期范围')
    }
  },
  {
    label: i18n.t('测试开始时间'),
    field: 'testStartTime',
    type: 'dateRange',
    props: {
      placeholder: i18n.t('请选择测试开始时间范围')
    }
  },
  {
    label: i18n.t('测试完成时间'),
    field: 'testEndTime',
    type: 'dateRange',
    props: {
      placeholder: i18n.t('请选择测试完成时间范围')
    }
  },
  {
    label: i18n.t('当前流程状态'),
    field: 'processStatus',
    type: 'select',
    options: processStatusOptions
  },
  {
    label: i18n.t('创建人'),
    field: 'createUserName',
    type: 'input',
    props: {
      placeholder: i18n.t('请输入创建人')
    }
  },
  {
    label: i18n.t('创建时间'),
    field: 'createTime',
    type: 'dateRange',
    props: {
      placeholder: i18n.t('请选择创建时间范围')
    }
  }
]
