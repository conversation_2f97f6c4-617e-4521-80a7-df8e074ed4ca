<!-- 样品确认单过程查询 -->
<template>
  <div>
    <common-list
      ref="commonListRef"
      :columns="columns"
      :toolbar="toolbar"
      :available-conditions="searchConditions"
      :required-conditions="requiredConditions"
      :enable-pagination="true"
      grid-id="dff1b56d-88ed-4d48-b78f-ca9af1c31253"
      search-grid-id="815a0a69-2ed9-41a9-8b7a-dc4ac8860613"
      @search="handleSearch"
      @searchConditionChange="handleSearchConditionChange"
      @templateSearch="handleTemplateSearch"
      @restoreSearchFormData="handleRestoreSearchFormData"
      @toolbarClick="handleToolbarClick"
      @pageChange="handlePageChange"
      @pageSizeChange="handlePageSizeChange"
    >
      <template #testReportDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleFileClick(row)">
          {{ $t('附件') }}
        </div>
      </template>
      <template #processStatusDefault="{ row }">
        <div style="cursor: pointer; color: #2783fe" @click="handleStatusClick(row)">
          {{ row.processStatusDesc }}
        </div>
      </template>
    </common-list>
  </div>
</template>

<script>
import CommonList from '@/components/CommonList'
import { getSearchFormItems } from './config/searchForm'
import { columnData, toolbar } from './config/index'
import { getHeadersFileName, download } from '@/utils/util'
import dayjs from 'dayjs'

export default {
  name: 'SampleProcess',
  components: {
    CommonList
  },
  data() {
    return {
      searchForm: {},
      columns: columnData,
      toolbar,
      requiredConditions: ['sampleNo'],
      tableData: [],
      currentPage: 1,
      pageSize: 50,
      // 标记是否已经进行过初始化搜索，避免重复搜索
      hasInitialSearch: false
    }
  },
  computed: {
    searchConditions() {
      const items = getSearchFormItems()
      return items
    }
  },
  mounted() {
    // 延迟执行初始搜索，给搜索模板初始化留出时间
    this.$nextTick(() => {
      setTimeout(() => {
        // 如果还没有进行过初始化搜索，则执行默认搜索
        if (!this.hasInitialSearch) {
          this.getTableData()
          this.hasInitialSearch = true
        }
      }, 100)
    })
  },
  methods: {
    handleSearchConditionChange(eventData) {
      const { value, fieldName } = eventData
      switch (fieldName) {
        case 'sendSampleDate':
        case 'testStartTime':
        case 'testEndTime':
        case 'createTime':
          this.handleDateTimeChange(value, fieldName)
          break
        default:
          this.searchForm[fieldName] = value
          break
      }
    },
    handleDateTimeChange(e, field) {
      if (!e?.startDate) {
        this.searchForm[`${field}S`] = null
        this.searchForm[`${field}E`] = null
        return
      }

      this.searchForm[`${field}S`] = this.getUnix(dayjs(e.startDate).format('YYYY-MM-DD 00:00:00'))
      this.searchForm[`${field}E`] = this.getUnix(dayjs(e.endDate).format('YYYY-MM-DD 23:59:59'))
    },
    getUnix(val) {
      return dayjs(val).valueOf()
    },
    handleSearch({ searchForm }) {
      console.log('handleSearch 接收到的 searchForm:', searchForm)
      console.log('searchForm 类型:', typeof searchForm)

      // 检查 searchForm 是否包含循环引用
      try {
        JSON.stringify(searchForm)
        console.log('searchForm 可以正常序列化')
        this.searchForm = searchForm
      } catch (e) {
        console.error('searchForm 包含循环引用:', e.message)
        console.log('尝试清理 searchForm...')

        // 清理 searchForm，只保留基本数据类型
        const cleanSearchForm = {}
        if (searchForm && typeof searchForm === 'object') {
          Object.keys(searchForm).forEach(key => {
            const value = searchForm[key]
            if (value !== null && value !== undefined) {
              if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
                cleanSearchForm[key] = value
              } else if (Array.isArray(value)) {
                const cleanArray = value.filter(item => {
                  try {
                    JSON.stringify(item)
                    return true
                  } catch (e) {
                    return false
                  }
                })
                if (cleanArray.length > 0) {
                  cleanSearchForm[key] = cleanArray
                }
              } else {
                try {
                  JSON.stringify(value)
                  cleanSearchForm[key] = value
                } catch (e) {
                  console.warn(`过滤掉包含循环引用的属性 ${key}`)
                }
              }
            }
          })
        }

        console.log('清理后的 searchForm:', cleanSearchForm)
        this.searchForm = cleanSearchForm
      }

      this.currentPage = 1
      this.getTableData()
    },
    handleTemplateSearch(templateData) {
      console.log('=== sampleProcess: 模板搜索事件触发 ===')
      console.log('接收到的模板数据:', templateData)
      console.log('当前搜索表单:', this.searchForm)

      // 直接处理模板搜索，触发查询
      this.applyTemplateData(templateData, true)

      console.log('=== sampleProcess: 模板搜索处理完成 ===')
    },

    // 处理默认模板的搜索条件恢复
    handleRestoreSearchFormData(conditions) {
      console.log('恢复搜索条件', conditions)

      // 恢复表单数据
      this.restoreFormData(conditions)

      // 如果还没有进行过初始化搜索，说明这是默认模板初始化
      // 需要等待 templateSearch 事件来触发搜索
      if (!this.hasInitialSearch) {
        console.log('默认模板初始化，等待 templateSearch 事件触发搜索')
      }
    },

    // 只恢复表单数据，不触发查询
    restoreFormData(conditions) {
      if (!conditions || Object.keys(conditions).length === 0) {
        return
      }

      console.log('恢复表单数据', conditions)

      // 处理日期范围字段的特殊逻辑
      Object.keys(conditions).forEach(key => {
        const value = conditions[key]

        // 检查是否是日期范围字段的结束时间戳
        if (key.endsWith('E') && typeof value === 'number') {
          const baseField = key.slice(0, -1) // 去掉末尾的 'E'
          const startKey = `${baseField}S`

          // 如果同时存在开始和结束时间戳，需要特殊处理
          if (conditions[startKey]) {
            // 这些字段需要通过 handleSearchConditionChange 来处理
            const dateFields = ['sendSampleDate', 'testStartTime', 'testEndTime', 'createTime']
            if (dateFields.includes(baseField)) {
              // 构造日期范围对象
              const dateRange = {
                startDate: new Date(conditions[startKey]),
                endDate: new Date(value)
              }
              this.handleDateTimeChange(dateRange, baseField)
              return
            }
          }
        }

        // 普通字段直接设置
        if (!key.endsWith('S') || !conditions[`${key.slice(0, -1)}E`]) {
          this.searchForm[key] = value
        }
      })
    },

    // 统一处理模板数据应用，避免重复查询
    applyTemplateData(conditions, isManualTemplate = false) {
      console.log(`=== applyTemplateData 开始 ===`)
      console.log(`类型: ${isManualTemplate ? '手动选择' : '自动恢复'}`)
      console.log('接收到的条件:', conditions)
      console.log('当前搜索表单:', this.searchForm)

      // 如果有搜索条件，先恢复表单数据
      if (conditions && Object.keys(conditions).length > 0) {
        console.log('恢复表单数据...')
        this.restoreFormData(conditions)
      } else {
        console.log('模板没有搜索条件，清空表单')
        // 如果模板没有搜索条件，清空当前搜索表单
        this.searchForm = {}
      }

      console.log('更新后的搜索表单:', this.searchForm)

      this.currentPage = 1
      this.hasInitialSearch = true

      console.log('准备触发搜索查询...')
      console.log('commonListRef 是否存在:', !!this.$refs.commonListRef)

      // 无论是否有搜索条件，都触发搜索
      this.getTableData()

      console.log(`=== applyTemplateData 完成 ===`)
    },
    async getTableData() {
      console.log('=== getTableData 开始执行 ===')
      console.log('当前页:', this.currentPage)
      console.log('页面大小:', this.pageSize)
      console.log('搜索表单原始数据:', this.searchForm)

      // 深度检查搜索表单中的每个属性
      console.log('搜索表单属性检查:')
      Object.keys(this.searchForm).forEach(key => {
        const value = this.searchForm[key]
        console.log(`  ${key}:`, typeof value, value)
        if (typeof value === 'object' && value !== null) {
          console.log(`    ${key} 是对象，检查是否包含循环引用...`)
          try {
            JSON.stringify(value)
            console.log(`    ${key} 可以正常序列化`)
          } catch (e) {
            console.error(`    ${key} 包含循环引用:`, e.message)
          }
        }
      })

      // 清理搜索表单，只保留基本数据类型
      const cleanSearchForm = {}
      Object.keys(this.searchForm).forEach(key => {
        const value = this.searchForm[key]
        if (value !== null && value !== undefined) {
          if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {
            cleanSearchForm[key] = value
          } else if (Array.isArray(value)) {
            // 检查数组中的元素
            const cleanArray = value.filter(item => {
              if (typeof item === 'string' || typeof item === 'number' || typeof item === 'boolean') {
                return true
              }
              try {
                JSON.stringify(item)
                return true
              } catch (e) {
                console.warn(`过滤掉包含循环引用的数组元素:`, item)
                return false
              }
            })
            if (cleanArray.length > 0) {
              cleanSearchForm[key] = cleanArray
            }
          } else {
            // 对于对象，尝试序列化
            try {
              JSON.stringify(value)
              cleanSearchForm[key] = value
            } catch (e) {
              console.warn(`过滤掉包含循环引用的属性 ${key}:`, e.message)
            }
          }
        }
      })

      console.log('清理后的搜索表单:', cleanSearchForm)

      const params = {
        page: {
          current: this.currentPage,
          size: this.pageSize
        },
        ...cleanSearchForm
      }

      console.log('API请求参数:', params)
      console.log('准备发送API请求...')

      this.$refs.commonListRef.setLoading(true)

      try {
        const res = await this.$API.sourceFiles.pageSampleProcessApi(params)
        console.log('API请求成功，响应:', res)

        this.$refs.commonListRef.setLoading(false)

        if (res?.code === 200) {
          console.log('响应码为200，处理数据...')
          const records = res.data?.records || []
          console.log('获取到的记录数:', records.length)

          this.$refs.commonListRef.setTableData(records)

          // 设置分页信息
          this.$refs.commonListRef.setPagination({
            total: Number(res.data?.total) || 0,
            current: Number(res.data?.current) || this.currentPage,
            size: Number(res.data?.size) || this.pageSize
          })

          // 同步分页状态
          this.currentPage = Number(res.data?.current) || this.currentPage
          this.pageSize = Number(res.data?.size) || this.pageSize

          console.log('数据处理完成')
        } else {
          console.log('响应码不是200:', res?.code)
        }
      } catch (error) {
        console.error('API请求失败:', error)
        this.$refs.commonListRef.setLoading(false)
      }

      console.log('=== getTableData 执行完成 ===')
    },
    async handleToolbarClick(item) {
      const actionMap = {
        export: () => this.handleExport(item)
      }

      const action = actionMap[item.code]
      if (action) {
        await action()
      }
    },
    handlePageChange({ page, pageSize, searchForm }) {
      this.currentPage = page
      this.pageSize = pageSize
      this.searchForm = searchForm
      this.getTableData()
    },
    handlePageSizeChange({ page, pageSize, searchForm }) {
      this.currentPage = page // 页面大小变化时重置到第一页
      this.pageSize = pageSize
      this.searchForm = searchForm
      this.getTableData()
    },
    async handleExport(item) {
      try {
        item.loading = true
        const params = {
          page: {
            current: this.currentPage,
            size: this.pageSize
          },
          ...this.searchForm
        }

        const res = await this.$API.sourceFiles.exportSampleProcessApi(params)
        const fileName = getHeadersFileName(res)
        download({ fileName: `${fileName}`, blob: res.data })
      } catch (error) {
        console.error('导出失败:', error)
        this.$toast({ content: error || this.$t('导出失败'), type: 'error' })
      } finally {
        item.loading = false
      }
    },
    handleFileClick(row) {
      // 处理附件点击事件
      if (!row?.id) {
        this.$toast({ content: '数据异常，请刷新页面重试', type: 'error' })
        return
      }

      this.$API.sourceFiles.fileListSampleProcessApi({ id: row.id }).then((res) => {
        if (res.code === 200) {
          let attachmentList = res.data
          this.$dialog({
            modal: () => import('./components/AttachmentDialog.vue'),
            data: {
              title: this.$t('附件列表'),
              attachmentList
            }
          })
        }
      })
    },
    handleStatusClick(row) {
      // 处理流程状态详情点击事件
      if (!row?.id) {
        this.$toast({ content: '数据异常，请刷新页面重试', type: 'error' })
        return
      }

      const processData = {
        id: row.id,
        sampleNo: row.sampleNo,
        sampleName: row.sampleName,
        processStatus: row.processStatus,
        processStatusDesc: row.processStatusDesc
      }

      this.$dialog({
        modal: () => import('./components/ProcessStatusDialog.vue'),
        data: {
          title: this.$t('样品流程状态'),
          processData
        }
      })
    }
  }
}
</script>
