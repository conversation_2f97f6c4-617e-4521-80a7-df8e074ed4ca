<template>
  <div class="full-height">
    <mt-template-page
      ref="templateRef"
      :template-config="pageConfig"
      :use-tool-template="false"
      :hidden-tabs="false"
      @handleClickToolBar="handleClickToolBar"
      @handleClickCellTitle="handleClickCellTitle"
      @handleCustomReset="handleCustomReset"
      @handleSelectTab="handleSelectTab"
    >
      <template v-slot:quick-search-form>
        <div class="custom-form-box">
          <!-- 金额计算失败明细tab的查询表单 -->
          <mt-form v-if="curTabeIndex === 3" ref="errorSearchFormRef" :model="errorSearchFormModel">
            <mt-form-item prop="rebateCode" :label="$t('返利协议单号')" label-style="top">
              <mt-input
                v-model="errorSearchFormModel.rebateCode"
                :show-clear-button="true"
                :placeholder="$t('请输入返利协议单号')"
              />
            </mt-form-item>
            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="errorSearchFormModel.companyCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="supplierCode" :label="$t('供应商')" label-style="top">
              <RemoteAutocomplete
                v-model="errorSearchFormModel.supplierCode"
                :multiple="false"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="supplier"
              ></RemoteAutocomplete>
            </mt-form-item>
          </mt-form>
          <!-- 其他tab的查询表单 -->
          <mt-form v-else ref="searchFormRef" :model="searchFormModel">
            <mt-form-item prop="rebateCode" :label="$t('返利协议单号')" label-style="top">
              <mt-input
                v-model="searchFormModel.rebateCode"
                :show-clear-button="true"
                :placeholder="$t('请输入返利协议单号')"
              />
            </mt-form-item>
            <mt-form-item prop="rebateName" :label="$t('返利协议名称')" label-style="top">
              <mt-input
                v-model="searchFormModel.rebateName"
                :show-clear-button="true"
                :placeholder="$t('请输入返利协议名称')"
              />
            </mt-form-item>

            <mt-form-item prop="companyCode" :label="$t('所属公司')" label-style="top">
              <RemoteAutocomplete
                :params="{
                  organizationLevelCodes: ['ORG02', 'ORG01'],
                  orgType: 'ORG001PRO',
                  includeItself: true
                }"
                v-model="searchFormModel.companyCode"
                :fields="{ text: 'orgName', value: 'orgCode' }"
                url="/masterDataManagement/tenant/organization/specified-level-paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="administrativeCompany"
              ></RemoteAutocomplete>
            </mt-form-item>

            <mt-form-item prop="supplierCodeList" :label="$t('供应商')" label-style="top">
              <RemoteAutocomplete
                v-model="searchFormModel.supplierCodeList"
                :multiple="true"
                :fields="{ text: 'supplierName', value: 'supplierCode' }"
                :search-fields="['supplierName', 'supplierCode']"
                url="/masterDataManagement/tenant/supplier/paged-query"
                :title-switch="false"
                :placeholder="$t('请选择')"
                select-type="supplier"
              ></RemoteAutocomplete>
            </mt-form-item>
            <mt-form-item prop="statusList" :label="$t('状态')" label-style="top">
              <mt-multi-select
                v-model="searchFormModel.statusList"
                css-class="rule-element"
                :data-source="statusList"
                :show-clear-button="true"
                :allow-filtering="true"
                filter-type="Contains"
                :placeholder="$t('请选择状态')"
              />
            </mt-form-item>
            <mt-form-item prop="createUserName" :label="$t('创建人')" label-style="top">
              <mt-input
                v-model="searchFormModel.createUserName"
                :show-clear-button="true"
                :placeholder="$t('请输入创建人')"
              />
            </mt-form-item>
            <mt-form-item prop="startDate" :label="$t('返利起始日')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.startDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择返利起始日')"
                @change="(e) => handleDateTimeChange(e, 'startDate')"
              />
            </mt-form-item>
            <mt-form-item prop="endDate" :label="$t('返利结束日')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.endDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择返利结束日')"
                @change="(e) => handleDateTimeChange(e, 'endDate')"
              />
            </mt-form-item>
            <mt-form-item prop="feedbackDate" :label="$t('要求反馈日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.feedbackDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择要求反馈日期')"
                @change="(e) => handleDateTimeChange(e, 'feedbackDate')"
              />
            </mt-form-item>
            <mt-form-item prop="creatDate" :label="$t('创建日期')" label-style="top">
              <mt-date-range-picker
                v-model="searchFormModel.creatDate"
                :allow-edit="false"
                :open-on-focus="true"
                :placeholder="$t('请选择创建日期')"
                @change="(e) => handleDateTimeChange(e, 'creatDate')"
              />
            </mt-form-item>
          </mt-form>
        </div>
      </template>
    </mt-template-page>
  </div>
</template>

<script>
import dayjs from 'dayjs'
import { pageConfig, statusList } from './config'
import { download, getHeadersFileName } from '@/utils/utils'
import RemoteAutocomplete from '@/components/RemoteAutocomplete'
import { getCurrentBu } from '@/constants/bu'

export default {
  components: { RemoteAutocomplete },
  provide() {
    return {
      searchFormModelList: [
        this.searchFormModel,
        this.searchFormModel,
        this.searchFormModel,
        this.errorSearchFormModel
      ] // 提供四个tab的表单模型
    }
  },
  data() {
    return {
      searchFormModel: {
        startDateRange: [],
        endDateRange: [],
        feedbackDateRange: [],
        creatDateRange: []
      },
      // 金额计算失败明细tab专用表单模型
      errorSearchFormModel: {
        rebateCode: null,
        companyCode: null,
        supplierCode: null
      },
      pageConfig,
      statusList,
      tempFormList: [{}, { statusList: [9] }, {}, {}], // 初始化四个tab的缓存对象
      curTabeIndex: 0
    }
  },
  computed: {},
  mounted() {
    this.$refs.templateRef.refreshCurrentGridData()
  },
  methods: {
    // 选择时间
    handleDateTimeChange(e, field) {
      if (e.startDate) {
        const start = dayjs(e.startDate).format('YYYY-MM-DD') + ' 00:00:00'
        const end = dayjs(e.endDate).format('YYYY-MM-DD') + ' 23:59:59'
        this.searchFormModel[field + 'Range'] = [new Date(start).getTime(), new Date(end).getTime()]
      } else {
        this.searchFormModel[field + 'Range'] = []
      }
    },
    // 切换tab页签
    handleSelectTab(index) {
      // 保存当前tab的表单数据
      if (this.curTabeIndex === 3) {
        // 当前是金额计算失败明细tab，保存errorSearchFormModel
        this.tempFormList[this.curTabeIndex] = { ...this.errorSearchFormModel }
      } else {
        // 当前是其他tab，保存searchFormModel
        this.tempFormList[this.curTabeIndex] = { ...this.searchFormModel }
      }

      // 重置表单
      this.handleCustomReset()

      // 恢复目标tab的表单数据
      if (index === 3) {
        // 切换到金额计算失败明细tab，恢复errorSearchFormModel
        for (const key in this.tempFormList[index]) {
          this.errorSearchFormModel[key] = this.tempFormList[index][key]
        }
      } else {
        // 切换到其他tab，恢复searchFormModel
        for (const key in this.tempFormList[index]) {
          this.searchFormModel[key] = this.tempFormList[index][key]
        }
      }

      this.curTabeIndex = index
      this.$nextTick(() => {
        this.$refs.templateRef.refreshCurrentGridData() // 切换tab后自动触发查询
      })
    },
    // 重置查询条件
    handleCustomReset() {
      // 重置通用表单模型
      for (const key in this.searchFormModel) {
        if (Object.hasOwnProperty.call(this.searchFormModel, key)) {
          this.searchFormModel[key] = null
        }
      }
      // 重置金额计算失败明细表单模型
      for (const key in this.errorSearchFormModel) {
        if (Object.hasOwnProperty.call(this.errorSearchFormModel, key)) {
          this.errorSearchFormModel[key] = null
        }
      }
    },
    // 点击工具栏按钮
    handleClickToolBar(e) {
      const { toolbar, gridRef } = e
      let selectedRecords = gridRef.getCustomSelectedRows()
      console.log(selectedRecords)
      if (selectedRecords.length === 0 && ['submit', 'recalculate'].includes(toolbar.id)) {
        this.$toast({ content: this.$t('请先选择一行'), type: 'warning' })
        return
      }
      switch (toolbar.id) {
        case 'Download':
          this.handleDownload()
          break
        case 'recalculate':
          this.handleRecalculate(selectedRecords)
          break
        default:
          break
      }
    },
    // 点击单元格标题
    handleClickCellTitle(e) {
      const { field, data } = e
      switch (field) {
        case 'rebateCode':
          this.handleToDetail('view', data)
          break
        default:
          break
      }
    },
    // 新增、编辑、查看
    handleToDetail(type, row) {
      localStorage.setItem('rebatePageType', 'edit')
      this.$router.push({
        name: 'rebate-calculat-amount-detail',
        query: {
          type,
          id: row?.id || null,
          tabIndex: this.curTabeIndex,
          refreshId: Date.now()
        }
      })
    },
    // 导出
    async handleDownload() {
      const page = this.$refs.templateRef.getCurrentUsefulRef().ejsRef.pageSettings
      let formData = {}

      // 根据当前tab选择对应的表单数据
      if (this.curTabeIndex === 3) {
        // 金额计算失败明细tab使用errorSearchFormModel
        formData = { ...this.errorSearchFormModel }
      } else {
        // 其他tab使用searchFormModel
        formData = { ...this.searchFormModel }
      }

      const params = {
        ...formData,
        queryType: this.curTabeIndex + 1,
        page,
        bu: getCurrentBu()
      }
      const res = await this.$API.rebateManagement.exportRebateAgreementList(params)
      if (res.data) {
        download({ fileName: getHeadersFileName(res), blob: res.data })
      }
    },
    // 重新计算
    async handleRecalculate(selectedRecords) {
      try {
        this.$loading()
        // 提取选中记录的ID
        const ids = selectedRecords.map(record => record.id)

        // 调用重新计算接口
        const res = await this.$API.rebateManagement.recalculateRebateAmount({ ids })

        if (res.code === 200) {
          this.$toast({ content: this.$t('重新计算成功'), type: 'success' })
          // 刷新当前表格数据
          this.$refs.templateRef.refreshCurrentGridData()
        } else {
          this.$toast({ content: res.msg || this.$t('重新计算失败'), type: 'error' })
        }
      } catch (error) {
        console.error('重新计算失败:', error)
        this.$toast({ content: error.msg || this.$t('重新计算失败'), type: 'error' })
      } finally {
        this.$hloading()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
@import '~@/styles/mixin.scss';

.full-height {
  height: 100%;
}
::v-deep {
  .template_checkbox {
    text-align: center;
    input[type='checkbox'] {
      visibility: visible;
    }
  }
  .j-select.ant-select .ant-select-selection {
    background-color: #f5f5f5;
    border-color: rgba(0, 0, 0, 0.42);
  }

  .e-rowcell.sticky-col-0,
  .e-headercell.sticky-col-0 {
    @include sticky-col;
    left: 0px;
  }
  .e-rowcell.sticky-col-1,
  .e-headercell.sticky-col-1 {
    @include sticky-col;
    left: 50px; // (注意：) 50px就是前面所有固定列的列宽相加，比如这里就是 0 + 复选框列（50px） = 50px
  }
}
</style>
