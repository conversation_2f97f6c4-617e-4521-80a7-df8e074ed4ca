

## 返利金额计算错误信息分页查询


**接口地址**:`/tenant/rebateHeader/calcErrorPageQuery`


**请求方式**:`POST`


**请求数据类型**:`application/json`


**响应数据类型**:`*/*`


**接口描述**:


**请求示例**:


```javascript
{
  "bu": "",
  "companyCode": "",
  "page": {
    "asc": [],
    "ascs": [],
    "countId": "",
    "current": 0,
    "desc": [],
    "descs": [],
    "hitCount": true,
    "maxLimit": 0,
    "optimizeCountSql": true,
    "orders": [
      {
        "asc": true,
        "column": ""
      }
    ],
    "pages": 0,
    "records": [],
    "searchCount": true,
    "size": 0,
    "total": 0
  },
  "rebateCode": "",
  "supplierCode": ""
}
```


**请求参数**:


**请求参数**:


| 参数名称 | 参数说明 | 请求类型    | 是否必须 | 数据类型 | schema |
| -------- | -------- | ----- | -------- | -------- | ------ |
|request|request|body|true|返利计算错误信息分页请求对象|返利计算错误信息分页请求对象|
|&emsp;&emsp;bu|事业部||false|string||
|&emsp;&emsp;companyCode|公司代码||false|string||
|&emsp;&emsp;page|分页||false|Page|Page|
|&emsp;&emsp;&emsp;&emsp;asc|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;ascs|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;countId|||false|string||
|&emsp;&emsp;&emsp;&emsp;current|||false|integer||
|&emsp;&emsp;&emsp;&emsp;desc|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;descs|||false|array|string|
|&emsp;&emsp;&emsp;&emsp;hitCount|||false|boolean||
|&emsp;&emsp;&emsp;&emsp;maxLimit|||false|integer||
|&emsp;&emsp;&emsp;&emsp;optimizeCountSql|||false|boolean||
|&emsp;&emsp;&emsp;&emsp;orders|||false|array|OrderItem|
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;asc|||false|boolean||
|&emsp;&emsp;&emsp;&emsp;&emsp;&emsp;column|||false|string||
|&emsp;&emsp;&emsp;&emsp;pages|||false|integer||
|&emsp;&emsp;&emsp;&emsp;records|||false|array|object|
|&emsp;&emsp;&emsp;&emsp;searchCount|||false|boolean||
|&emsp;&emsp;&emsp;&emsp;size|||false|integer||
|&emsp;&emsp;&emsp;&emsp;total|||false|integer||
|&emsp;&emsp;rebateCode|返利协议代码||false|string||
|&emsp;&emsp;supplierCode|供应商编码||false|string||


**响应状态**:


| 状态码 | 说明 | schema |
| -------- | -------- | ----- | 
|200|OK|通用返回统一封装对象«IPage«List«RebateCalcFailInfo对象»»»|
|201|Created||
|401|Unauthorized||
|403|Forbidden||
|404|Not Found||


**响应参数**:


| 参数名称 | 参数说明 | 类型 | schema |
| -------- | -------- | ----- |----- | 
|code|响应编码|integer(int32)|integer(int32)|
|data|数据|IPage«List«RebateCalcFailInfo对象»»|IPage«List«RebateCalcFailInfo对象»»|
|&emsp;&emsp;current||integer(int64)||
|&emsp;&emsp;hitCount||boolean||
|&emsp;&emsp;pages||integer(int64)||
|&emsp;&emsp;records|返利计算失败信息记录表|array|RebateCalcFailInfo对象|
|&emsp;&emsp;&emsp;&emsp;abolished|删除标识(架构字段)|integer||
|&emsp;&emsp;&emsp;&emsp;calcTime|金额计算时间|integer||
|&emsp;&emsp;&emsp;&emsp;categoryCode|品类编码(SRM)|string||
|&emsp;&emsp;&emsp;&emsp;categoryName|品类名称(SRM)|string||
|&emsp;&emsp;&emsp;&emsp;companyCode|公司编码|string||
|&emsp;&emsp;&emsp;&emsp;companyName|公司名称|string||
|&emsp;&emsp;&emsp;&emsp;factoryCode|工厂编码|string||
|&emsp;&emsp;&emsp;&emsp;factoryName|工厂名称|string||
|&emsp;&emsp;&emsp;&emsp;id|ID|integer||
|&emsp;&emsp;&emsp;&emsp;materialCode|物料编码|string||
|&emsp;&emsp;&emsp;&emsp;materialName|物料名称|string||
|&emsp;&emsp;&emsp;&emsp;rebateCode|返利协议单编码|string||
|&emsp;&emsp;&emsp;&emsp;remark|备注|string||
|&emsp;&emsp;&emsp;&emsp;round|轮次|integer||
|&emsp;&emsp;&emsp;&emsp;supplierCode|供应商编码|string||
|&emsp;&emsp;&emsp;&emsp;supplierName|供应商名称|string||
|&emsp;&emsp;searchCount||boolean||
|&emsp;&emsp;size||integer(int64)||
|&emsp;&emsp;total||integer(int64)||
|errorStackTrace|错误堆栈信息|string||
|msg|提示信息|string||
|success|是否成功|boolean||
|traceId|追踪ID|string||


**响应示例**:
```javascript
{
	"code": 0,
	"data": {
		"current": 0,
		"hitCount": true,
		"pages": 0,
		"records": [
			{
				"abolished": 0,
				"calcTime": 0,
				"categoryCode": "",
				"categoryName": "",
				"companyCode": "",
				"companyName": "",
				"factoryCode": "",
				"factoryName": "",
				"id": 0,
				"materialCode": "",
				"materialName": "",
				"rebateCode": "",
				"remark": "",
				"round": 0,
				"supplierCode": "",
				"supplierName": ""
			}
		],
		"searchCount": true,
		"size": 0,
		"total": 0
	},
	"errorStackTrace": "",
	"msg": "",
	"success": true,
	"traceId": ""
}
```